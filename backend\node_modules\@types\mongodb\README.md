# Installation
> `npm install --save @types/mongodb`

# Summary
This package contains type definitions for MongoDB (https://github.com/mongodb/node-mongodb-native).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/mongodb.

### Additional Details
 * Last updated: Wed, 07 Jul 2021 00:01:43 GMT
 * Dependencies: [@types/bson](https://npmjs.com/package/@types/bson), [@types/node](https://npmjs.com/package/@types/node)
 * Global values: none

# Credits
These definitions were written by [<PERSON>](https://github.com/CaselIT), [<PERSON>](https://github.com/alanmarcell), [Gaurav <PERSON>](https://github.com/dante-101), [<PERSON>](https://github.com/mcortesi), [<PERSON>](https://github.com/<PERSON>), [<PERSON>](https://github.com/AJCStriker), [<PERSON>](https://github.com/julien-c), [<PERSON>](https://github.com/dapra<PERSON>ian), [<PERSON>ys <PERSON>ulyak](https://github.com/denys-bushulyak), [Bastien Arata](https://github.com/b4nst), [Wan Bachtiar](https://github.com/sindbach), [Geraldine Lemeur](https://github.com/geraldinelemeur), [Dominik Heigl](https://github.com/various89), [Angela-1](https://github.com/angela-1), [Hector Ribes](https://github.com/hector7), [Florian Richter](https://github.com/floric), [Erik Christensen](https://github.com/erikc5000), [Nick Zahn](https://github.com/Manc), [Jarom Loveridge](https://github.com/jloveridge), [Luis Pais](https://github.com/ranguna), [Hossein Saniei](https://github.com/HosseinAgha), [Alberto Silva](https://github.com/albertossilva), [Piotr Błażejewicz](https://github.com/peterblazejewicz), [Linus Unnebäck](https://github.com/LinusU), [Richard Bateman](https://github.com/taxilian), [Igor Strebezhev](https://github.com/xamgore), [Valentin Agachi](https://github.com/avaly), [HitkoDev](https://github.com/HitkoDev), [TJT](https://github.com/Celend), [Julien TASSIN](https://github.com/jtassin), [Anna Henningsen](https://github.com/addaleax), [Emmanuel Gautier](https://github.com/emmanuelgautier), [Wyatt Johnson](https://github.com/wyattjoh), and [Boris Figovsky](https://github.com/borfig).
