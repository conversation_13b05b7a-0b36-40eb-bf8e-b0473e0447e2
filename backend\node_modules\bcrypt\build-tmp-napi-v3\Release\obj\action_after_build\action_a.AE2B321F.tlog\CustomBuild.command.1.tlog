^D:\STUDIES\6TH SEMESTER\SOFTWARE ENGG\THISSSSSSSSSSS\MERN-BUS-APP-MASTER\MERN-BUS-APP-MASTER\BACKEND\NODE_MODULES\BCRYPT\BUILD\RELEASE\BCRYPT_LIB.NODE
call mkdir "D:\STUDIES\6TH SEMESTER\SOFTWARE ENGG\thisssssssssss\MERN-BUS-APP-master\MERN-BUS-APP-master\backend\node_modules\bcrypt\lib\binding\napi-v3" 2>nul & set ERRORLEVEL=0 & copy /Y "D:\STUDIES\6TH SEMESTER\SOFTWARE ENGG\thisssssssssss\MERN-BUS-APP-master\MERN-BUS-APP-master\backend\node_modules\bcrypt\build\Release\bcrypt_lib.node" "D:\STUDIES\6TH SEMESTER\SOFTWARE ENGG\thisssssssssss\MERN-BUS-APP-master\MERN-BUS-APP-master\backend\node_modules\bcrypt\lib\binding\napi-v3\bcrypt_lib.node"
if %errorlevel% neq 0 exit /b %errorlevel%
