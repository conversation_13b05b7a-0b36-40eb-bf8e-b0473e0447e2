{"name": "ignore-walk", "version": "3.0.4", "description": "Nested/recursive `.gitignore`/`.npmignore` parsing and filtering.", "main": "index.js", "devDependencies": {"mkdirp": "^0.5.1", "mutate-fs": "^1.1.0", "rimraf": "^2.6.1", "tap": "^15.0.6"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "keywords": ["ignorefile", "ignore", "file", ".giti<PERSON>re", ".n<PERSON><PERSON><PERSON>", "glob"], "author": "<PERSON> <<EMAIL>> (http://blog.izs.me/)", "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/isaacs/ignore-walk.git"}, "files": ["index.js"], "dependencies": {"minimatch": "^3.0.4"}, "tap": {"test-env": "LC_ALL=sk", "before": "test/00-setup.js", "after": "test/zz-cleanup.js", "jobs": 1}}