{"name": "cookie", "description": "HTTP server cookie parsing and serialization", "version": "0.4.1", "author": "<PERSON>ylman <<EMAIL>>", "contributors": ["<PERSON> <<EMAIL>>"], "license": "MIT", "keywords": ["cookie", "cookies"], "repository": "jshttp/cookie", "devDependencies": {"beautify-benchmark": "0.2.4", "benchmark": "2.1.4", "eslint": "6.8.0", "eslint-plugin-markdown": "1.0.2", "mocha": "7.1.1", "nyc": "15.0.1"}, "files": ["HISTORY.md", "LICENSE", "README.md", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"bench": "node benchmark/index.js", "lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --bail --check-leaks --ui qunit test/", "test-ci": "nyc --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test", "version": "node scripts/version-history.js && git add HISTORY.md"}}