{"name": "bson", "description": "A bson parser for node.js and the browser", "keywords": ["mongodb", "bson", "parser"], "files": ["lib", "index.js", "browser_build", "bower.json"], "version": "1.1.6", "author": "<PERSON> <<EMAIL>>", "contributors": [], "repository": "mongodb/js-bson", "bugs": {"mail": "<EMAIL>", "url": "https://github.com/mongodb/js-bson/issues"}, "devDependencies": {"benchmark": "1.0.0", "colors": "1.1.0", "nodeunit": "0.9.0", "babel-core": "^6.14.0", "babel-loader": "^6.2.5", "babel-polyfill": "^6.13.0", "babel-preset-es2015": "^6.14.0", "babel-preset-stage-0": "^6.5.0", "babel-register": "^6.14.0", "conventional-changelog-cli": "^1.3.5", "standard-version": "^9.1.1", "webpack": "^1.13.2", "webpack-polyfills-plugin": "0.0.9"}, "config": {"native": false}, "main": "./index", "directories": {"lib": "./lib/bson"}, "engines": {"node": ">=0.6.19"}, "scripts": {"test": "nodeunit ./test/node", "build": "webpack --config ./webpack.dist.config.js", "changelog": "conventional-changelog -p angular -i HISTORY.md -s", "lint": "eslint lib test", "format": "prettier --print-width 100 --tab-width 2 --single-quote --write 'test/**/*.js' 'lib/**/*.js'", "release": "standard-version -i HISTORY.md"}, "browser": "lib/bson/bson.js", "license": "Apache-2.0"}