{"name": "on-finished", "description": "Execute a callback when a request closes, finishes, or errors", "version": "2.3.0", "contributors": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>> (http://jongleberry.com)"], "license": "MIT", "repository": "jshttp/on-finished", "dependencies": {"ee-first": "1.1.1"}, "devDependencies": {"istanbul": "0.3.9", "mocha": "2.2.5"}, "engines": {"node": ">= 0.8"}, "files": ["HISTORY.md", "LICENSE", "index.js"], "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}}