{"name": "code-point-at", "version": "1.1.0", "description": "ES2015 `String#codePointAt()` ponyfill", "license": "MIT", "repository": "sindresorhus/code-point-at", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["es2015", "ponyfill", "polyfill", "shim", "string", "str", "code", "point", "at", "codepoint", "unicode"], "devDependencies": {"ava": "*", "xo": "^0.16.0"}}