{"name": "express", "description": "Fast, unopinionated, minimalist web framework", "version": "4.16.4", "author": "<PERSON><PERSON> <<EMAIL>>", "contributors": ["<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON>htylman <<EMAIL>>", "<PERSON> <PERSON> <<EMAIL>>"], "license": "MIT", "repository": "expressjs/express", "homepage": "http://expressjs.com/", "keywords": ["express", "framework", "sinatra", "web", "rest", "restful", "router", "app", "api"], "dependencies": {"accepts": "~1.3.5", "array-flatten": "1.1.1", "body-parser": "1.18.3", "content-disposition": "0.5.2", "content-type": "~1.0.4", "cookie": "0.3.1", "cookie-signature": "1.0.6", "debug": "2.6.9", "depd": "~1.1.2", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "etag": "~1.8.1", "finalhandler": "1.1.1", "fresh": "0.5.2", "merge-descriptors": "1.0.1", "methods": "~1.1.2", "on-finished": "~2.3.0", "parseurl": "~1.3.2", "path-to-regexp": "0.1.7", "proxy-addr": "~2.0.4", "qs": "6.5.2", "range-parser": "~1.2.0", "safe-buffer": "5.1.2", "send": "0.16.2", "serve-static": "1.13.2", "setprototypeof": "1.1.0", "statuses": "~1.4.0", "type-is": "~1.6.16", "utils-merge": "1.0.1", "vary": "~1.1.2"}, "devDependencies": {"after": "0.8.2", "connect-redis": "3.4.0", "cookie-parser": "~1.4.3", "cookie-session": "1.3.2", "ejs": "2.6.1", "eslint": "2.13.1", "express-session": "1.15.6", "hbs": "4.0.1", "istanbul": "0.4.5", "marked": "0.5.1", "method-override": "3.0.0", "mocha": "5.2.0", "morgan": "1.9.1", "multiparty": "4.2.1", "pbkdf2-password": "1.2.1", "should": "13.2.3", "supertest": "3.3.0", "vhost": "~3.0.2"}, "engines": {"node": ">= 0.10.0"}, "files": ["LICENSE", "History.md", "Readme.md", "index.js", "lib/"], "scripts": {"lint": "eslint .", "test": "mocha --require test/support/env --reporter spec --bail --check-leaks test/ test/acceptance/", "test-ci": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --require test/support/env --reporter spec --check-leaks test/ test/acceptance/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --require test/support/env --reporter dot --check-leaks test/ test/acceptance/", "test-tap": "mocha --require test/support/env --reporter tap --check-leaks test/ test/acceptance/"}}