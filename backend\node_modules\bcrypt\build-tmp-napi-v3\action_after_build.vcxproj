<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{AE2B321F-BC72-4FC0-B56C-902B154A1B82}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <RootNamespace>action_after_build</RootNamespace>
    <IgnoreWarnCompileDuplicatedFilename>true</IgnoreWarnCompileDuplicatedFilename>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
    <WindowsTargetPlatformVersion>10.0.19041.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props"/>
  <PropertyGroup Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
  </PropertyGroup>
  <PropertyGroup Label="Locals">
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props"/>
  <Import Project="$(VCTargetsPath)\BuildCustomizations\masm.props"/>
  <ImportGroup Label="ExtensionSettings"/>
  <ImportGroup Label="PropertySheets">
    <Import Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props"/>
  </ImportGroup>
  <PropertyGroup Label="UserMacros"/>
  <PropertyGroup>
    <ExecutablePath>$(ExecutablePath);$(MSBuildProjectDirectory)\..\bin\;$(MSBuildProjectDirectory)\..\bin\</ExecutablePath>
    <IntDir>$(Configuration)\obj\$(ProjectName)\</IntDir>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkIncremental>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</LinkIncremental>
    <OutDir>$(SolutionDir)$(Configuration)\</OutDir>
    <TargetName>$(ProjectName)</TargetName>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\AppData\Local\node-gyp\Cache\16.13.2\include\node;C:\Users\<USER>\AppData\Local\node-gyp\Cache\16.13.2\src;C:\Users\<USER>\AppData\Local\node-gyp\Cache\16.13.2\deps\openssl\config;C:\Users\<USER>\AppData\Local\node-gyp\Cache\16.13.2\deps\openssl\openssl\include;C:\Users\<USER>\AppData\Local\node-gyp\Cache\16.13.2\deps\uv\include;C:\Users\<USER>\AppData\Local\node-gyp\Cache\16.13.2\deps\zlib;C:\Users\<USER>\AppData\Local\node-gyp\Cache\16.13.2\deps\v8\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>/Zc:__cplusplus %(AdditionalOptions)</AdditionalOptions>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <BufferSecurityCheck>true</BufferSecurityCheck>
      <DebugInformationFormat>OldStyle</DebugInformationFormat>
      <DisableSpecificWarnings>4351;4355;4800;4251;4275;4244;4267;%(DisableSpecificWarnings)</DisableSpecificWarnings>
      <ExceptionHandling>false</ExceptionHandling>
      <MinimalRebuild>false</MinimalRebuild>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <OmitFramePointers>false</OmitFramePointers>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <PreprocessorDefinitions>NODE_GYP_MODULE_NAME=action_after_build;USING_UV_SHARED=1;USING_V8_SHARED=1;V8_DEPRECATION_WARNINGS=1;V8_DEPRECATION_WARNINGS;V8_IMMINENT_DEPRECATION_WARNINGS;_GLIBCXX_USE_CXX11_ABI=1;WIN32;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_HAS_EXCEPTIONS=0;OPENSSL_NO_PINSHARED;OPENSSL_THREADS;HOST_BINARY=&quot;node.exe&quot;;DEBUG;_DEBUG;V8_ENABLE_CHECKS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreadedDebug</RuntimeLibrary>
      <StringPooling>true</StringPooling>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TreatWarningAsError>false</TreatWarningAsError>
      <WarningLevel>Level3</WarningLevel>
      <WholeProgramOptimization>true</WholeProgramOptimization>
    </ClCompile>
    <Lib>
      <AdditionalOptions>/LTCG:INCREMENTAL %(AdditionalOptions)</AdditionalOptions>
    </Lib>
    <Link>
      <AdditionalDependencies>kernel32.lib;user32.lib;gdi32.lib;winspool.lib;comdlg32.lib;advapi32.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;odbc32.lib;DelayImp.lib;&quot;C:\\Users\\<USER>\\AppData\\Local\\node-gyp\\Cache\\16.13.2\\x64\\node.lib&quot;</AdditionalDependencies>
      <AdditionalOptions>/LTCG:INCREMENTAL /ignore:4199 %(AdditionalOptions)</AdditionalOptions>
      <DelayLoadDLLs>node.exe;%(DelayLoadDLLs)</DelayLoadDLLs>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <OptimizeReferences>true</OptimizeReferences>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TargetMachine>MachineX64</TargetMachine>
    </Link>
    <ResourceCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\AppData\Local\node-gyp\Cache\16.13.2\include\node;C:\Users\<USER>\AppData\Local\node-gyp\Cache\16.13.2\src;C:\Users\<USER>\AppData\Local\node-gyp\Cache\16.13.2\deps\openssl\config;C:\Users\<USER>\AppData\Local\node-gyp\Cache\16.13.2\deps\openssl\openssl\include;C:\Users\<USER>\AppData\Local\node-gyp\Cache\16.13.2\deps\uv\include;C:\Users\<USER>\AppData\Local\node-gyp\Cache\16.13.2\deps\zlib;C:\Users\<USER>\AppData\Local\node-gyp\Cache\16.13.2\deps\v8\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>NODE_GYP_MODULE_NAME=action_after_build;USING_UV_SHARED=1;USING_V8_SHARED=1;V8_DEPRECATION_WARNINGS=1;V8_DEPRECATION_WARNINGS;V8_IMMINENT_DEPRECATION_WARNINGS;_GLIBCXX_USE_CXX11_ABI=1;WIN32;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_HAS_EXCEPTIONS=0;OPENSSL_NO_PINSHARED;OPENSSL_THREADS;HOST_BINARY=&quot;node.exe&quot;;DEBUG;_DEBUG;V8_ENABLE_CHECKS;%(PreprocessorDefinitions);%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ResourceCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\AppData\Local\node-gyp\Cache\16.13.2\include\node;C:\Users\<USER>\AppData\Local\node-gyp\Cache\16.13.2\src;C:\Users\<USER>\AppData\Local\node-gyp\Cache\16.13.2\deps\openssl\config;C:\Users\<USER>\AppData\Local\node-gyp\Cache\16.13.2\deps\openssl\openssl\include;C:\Users\<USER>\AppData\Local\node-gyp\Cache\16.13.2\deps\uv\include;C:\Users\<USER>\AppData\Local\node-gyp\Cache\16.13.2\deps\zlib;C:\Users\<USER>\AppData\Local\node-gyp\Cache\16.13.2\deps\v8\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>/Zc:__cplusplus %(AdditionalOptions)</AdditionalOptions>
      <BufferSecurityCheck>true</BufferSecurityCheck>
      <DebugInformationFormat>OldStyle</DebugInformationFormat>
      <DisableSpecificWarnings>4351;4355;4800;4251;4275;4244;4267;%(DisableSpecificWarnings)</DisableSpecificWarnings>
      <ExceptionHandling>false</ExceptionHandling>
      <FavorSizeOrSpeed>Speed</FavorSizeOrSpeed>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <OmitFramePointers>true</OmitFramePointers>
      <Optimization>Full</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <PreprocessorDefinitions>NODE_GYP_MODULE_NAME=action_after_build;USING_UV_SHARED=1;USING_V8_SHARED=1;V8_DEPRECATION_WARNINGS=1;V8_DEPRECATION_WARNINGS;V8_IMMINENT_DEPRECATION_WARNINGS;_GLIBCXX_USE_CXX11_ABI=1;WIN32;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_HAS_EXCEPTIONS=0;OPENSSL_NO_PINSHARED;OPENSSL_THREADS;HOST_BINARY=&quot;node.exe&quot;;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <RuntimeTypeInfo>false</RuntimeTypeInfo>
      <StringPooling>true</StringPooling>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TreatWarningAsError>false</TreatWarningAsError>
      <WarningLevel>Level3</WarningLevel>
      <WholeProgramOptimization>true</WholeProgramOptimization>
    </ClCompile>
    <Lib>
      <AdditionalOptions>/LTCG:INCREMENTAL %(AdditionalOptions)</AdditionalOptions>
    </Lib>
    <Link>
      <AdditionalDependencies>kernel32.lib;user32.lib;gdi32.lib;winspool.lib;comdlg32.lib;advapi32.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;odbc32.lib;DelayImp.lib;&quot;C:\\Users\\<USER>\\AppData\\Local\\node-gyp\\Cache\\16.13.2\\x64\\node.lib&quot;</AdditionalDependencies>
      <AdditionalOptions>/LTCG:INCREMENTAL /ignore:4199 %(AdditionalOptions)</AdditionalOptions>
      <DelayLoadDLLs>node.exe;%(DelayLoadDLLs)</DelayLoadDLLs>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <OptimizeReferences>true</OptimizeReferences>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TargetMachine>MachineX64</TargetMachine>
    </Link>
    <ResourceCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\AppData\Local\node-gyp\Cache\16.13.2\include\node;C:\Users\<USER>\AppData\Local\node-gyp\Cache\16.13.2\src;C:\Users\<USER>\AppData\Local\node-gyp\Cache\16.13.2\deps\openssl\config;C:\Users\<USER>\AppData\Local\node-gyp\Cache\16.13.2\deps\openssl\openssl\include;C:\Users\<USER>\AppData\Local\node-gyp\Cache\16.13.2\deps\uv\include;C:\Users\<USER>\AppData\Local\node-gyp\Cache\16.13.2\deps\zlib;C:\Users\<USER>\AppData\Local\node-gyp\Cache\16.13.2\deps\v8\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>NODE_GYP_MODULE_NAME=action_after_build;USING_UV_SHARED=1;USING_V8_SHARED=1;V8_DEPRECATION_WARNINGS=1;V8_DEPRECATION_WARNINGS;V8_IMMINENT_DEPRECATION_WARNINGS;_GLIBCXX_USE_CXX11_ABI=1;WIN32;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_HAS_EXCEPTIONS=0;OPENSSL_NO_PINSHARED;OPENSSL_THREADS;HOST_BINARY=&quot;node.exe&quot;;%(PreprocessorDefinitions);%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ResourceCompile>
  </ItemDefinitionGroup>
  <ItemGroup>
    <None Include="..\binding.gyp"/>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="C:\Program Files\nodejs\node_modules\npm\node_modules\node-gyp\src\win_delay_load_hook.cc"/>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="$(OutDir)\bcrypt_lib.node">
      <FileType>Document</FileType>
      <Command>call mkdir &quot;D:\STUDIES\6TH SEMESTER\SOFTWARE ENGG\thisssssssssss\MERN-BUS-APP-master\MERN-BUS-APP-master\backend\node_modules\bcrypt\lib\binding\napi-v3&quot; 2&gt;nul &amp; set ERRORLEVEL=0 &amp; copy /Y &quot;$(OutDir)bcrypt_lib.node&quot; &quot;D:\STUDIES\6TH SEMESTER\SOFTWARE ENGG\thisssssssssss\MERN-BUS-APP-master\MERN-BUS-APP-master\backend\node_modules\bcrypt\lib\binding\napi-v3\bcrypt_lib.node&quot;&#xD;&#xA;if %errorlevel% neq 0 exit /b %errorlevel%</Command>
      <Message>Copying $(OutDir)/bcrypt_lib.node to D:\STUDIES\6TH SEMESTER\SOFTWARE ENGG\thisssssssssss\MERN-BUS-APP-master\MERN-BUS-APP-master\backend\node_modules\bcrypt\lib\binding\napi-v3</Message>
      <Outputs>D:\STUDIES\6TH SEMESTER\SOFTWARE ENGG\thisssssssssss\MERN-BUS-APP-master\MERN-BUS-APP-master\backend\node_modules\bcrypt\lib\binding\napi-v3\bcrypt_lib.node</Outputs>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="bcrypt_lib.vcxproj">
      <Project>{912B0C4B-16F0-A9B6-AC44-6B805B62922C}</Project>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets"/>
  <Import Project="$(VCTargetsPath)\BuildCustomizations\masm.targets"/>
  <ImportGroup Label="ExtensionTargets"/>
</Project>
